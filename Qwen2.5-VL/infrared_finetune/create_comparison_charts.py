#!/usr/bin/env python3
"""
创建红外微小目标检测方法对比图表
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def create_overall_comparison():
    """创建总体性能对比图"""
    methods = ['Qwen原模型', 'Qwen微调后', 'YOLO']
    recall = [3.0, 31.5, 95.0]
    fp_rate = [95.6, 59.7, 3.3]
    consistency = [0, 0, 100.0]
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 召回率对比
    bars1 = ax1.bar(methods, recall, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax1.set_title('召回率对比 (%)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('召回率 (%)')
    ax1.set_ylim(0, 100)
    for i, v in enumerate(recall):
        ax1.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')
    
    # 虚警率对比
    bars2 = ax2.bar(methods, fp_rate, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax2.set_title('虚警率对比 (%)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('虚警率 (%)')
    ax2.set_ylim(0, 100)
    for i, v in enumerate(fp_rate):
        ax2.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')
    
    # 时空一致性对比
    bars3 = ax3.bar(methods, consistency, color=['#ff6b6b', '#4ecdc4', '#45b7d1'], alpha=0.8)
    ax3.set_title('时空一致性对比 (%)', fontsize=14, fontweight='bold')
    ax3.set_ylabel('一致性 (%)')
    ax3.set_ylim(0, 100)
    for i, v in enumerate(consistency):
        ax3.text(i, v + 2, f'{v}%', ha='center', fontweight='bold')
    
    # 综合性能雷达图
    categories = ['召回率', '低虚警率', '时空一致性']
    # 将虚警率转换为"低虚警率"分数 (100 - 虚警率)
    low_fp_rate = [100 - x for x in fp_rate]
    
    qwen_orig = [recall[0], low_fp_rate[0], consistency[0]]
    qwen_tuned = [recall[1], low_fp_rate[1], consistency[1]]
    yolo = [recall[2], low_fp_rate[2], consistency[2]]
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    qwen_orig += qwen_orig[:1]
    qwen_tuned += qwen_tuned[:1]
    yolo += yolo[:1]
    
    ax4 = plt.subplot(2, 2, 4, projection='polar')
    ax4.plot(angles, qwen_orig, 'o-', linewidth=2, label='Qwen原模型', color='#ff6b6b')
    ax4.fill(angles, qwen_orig, alpha=0.25, color='#ff6b6b')
    ax4.plot(angles, qwen_tuned, 'o-', linewidth=2, label='Qwen微调后', color='#4ecdc4')
    ax4.fill(angles, qwen_tuned, alpha=0.25, color='#4ecdc4')
    ax4.plot(angles, yolo, 'o-', linewidth=2, label='YOLO', color='#45b7d1')
    ax4.fill(angles, yolo, alpha=0.25, color='#45b7d1')
    
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(categories)
    ax4.set_ylim(0, 100)
    ax4.set_title('综合性能对比', fontsize=14, fontweight='bold', pad=20)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('overall_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_sequence_comparison():
    """创建各序列详细对比"""
    sequences = ['data01', 'data02', 'data04', 'data05', 'data06', 'data07', 'data23', 'data25', 'data26']
    
    # Qwen微调后结果
    qwen_recall = [33.3, 30.4, 32.8, 36.1, 29.5, 34.4, 32.8, 30.2, 28.9]
    qwen_fp = [60.0, 59.1, 57.4, 62.3, 59.0, 60.7, 58.6, 58.7, 61.3]
    
    # YOLO结果
    yolo_recall = [97.3, 90.4, 100.0, 100.0, 93.4, 100.0, 96.9, 78.9, 91.8]
    yolo_fp = [6.4, 1.0, 0.0, 7.6, 1.7, 3.2, 7.5, 3.6, 3.2]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    x = np.arange(len(sequences))
    width = 0.35
    
    # 召回率对比
    bars1 = ax1.bar(x - width/2, qwen_recall, width, label='Qwen微调后', color='#4ecdc4', alpha=0.8)
    bars2 = ax1.bar(x + width/2, yolo_recall, width, label='YOLO', color='#45b7d1', alpha=0.8)
    
    ax1.set_title('各序列召回率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('召回率 (%)')
    ax1.set_xlabel('序列')
    ax1.set_xticks(x)
    ax1.set_xticklabels(sequences, rotation=45)
    ax1.legend()
    ax1.set_ylim(0, 105)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    
    # 虚警率对比
    bars3 = ax2.bar(x - width/2, qwen_fp, width, label='Qwen微调后', color='#4ecdc4', alpha=0.8)
    bars4 = ax2.bar(x + width/2, yolo_fp, width, label='YOLO', color='#45b7d1', alpha=0.8)
    
    ax2.set_title('各序列虚警率对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('虚警率 (%)')
    ax2.set_xlabel('序列')
    ax2.set_xticks(x)
    ax2.set_xticklabels(sequences, rotation=45)
    ax2.legend()
    ax2.set_ylim(0, 70)
    
    # 添加数值标签
    for bar in bars3:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    for bar in bars4:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.savefig('sequence_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_improvement_analysis():
    """创建改进效果分析图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 微调前后对比
    metrics = ['召回率', '虚警率']
    before = [3.0, 95.6]
    after = [31.5, 59.7]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, before, width, label='微调前', color='#ff6b6b', alpha=0.8)
    bars2 = ax1.bar(x + width/2, after, width, label='微调后', color='#4ecdc4', alpha=0.8)
    
    ax1.set_title('Qwen模型微调前后对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('百分比 (%)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics)
    ax1.legend()
    ax1.set_ylim(0, 100)
    
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{height}%', ha='center', va='bottom', fontweight='bold')
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{height}%', ha='center', va='bottom', fontweight='bold')
    
    # 改进幅度
    improvements = ['召回率提升', '虚警率降低']
    values = [31.5 - 3.0, 95.6 - 59.7]
    colors = ['#2ecc71', '#e74c3c']
    
    bars = ax2.bar(improvements, values, color=colors, alpha=0.8)
    ax2.set_title('微调改进幅度', fontsize=14, fontweight='bold')
    ax2.set_ylabel('改进幅度 (百分点)')
    
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'+{height:.1f}pp', ha='center', va='bottom', fontweight='bold')
    
    # 与YOLO的性能差距
    gap_metrics = ['召回率差距', '虚警率差距']
    gaps = [95.0 - 31.5, 59.7 - 3.3]
    
    bars = ax3.bar(gap_metrics, gaps, color=['#e67e22', '#e67e22'], alpha=0.8)
    ax3.set_title('与YOLO的性能差距', fontsize=14, fontweight='bold')
    ax3.set_ylabel('差距 (百分点)')
    
    for bar in bars:
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}pp', ha='center', va='bottom', fontweight='bold')
    
    # 性能提升倍数
    multipliers = ['召回率提升倍数']
    mult_values = [31.5 / 3.0]
    
    bars = ax4.bar(multipliers, mult_values, color='#9b59b6', alpha=0.8)
    ax4.set_title('微调效果倍数', fontsize=14, fontweight='bold')
    ax4.set_ylabel('提升倍数')
    
    for bar in bars:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{height:.1f}x', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('improvement_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("🎨 生成红外微小目标检测方法对比图表...")
    
    print("📊 创建总体性能对比图...")
    create_overall_comparison()
    
    print("📈 创建各序列详细对比图...")
    create_sequence_comparison()
    
    print("📉 创建改进效果分析图...")
    create_improvement_analysis()
    
    print("✅ 所有图表生成完成！")
    print("生成的图表文件:")
    print("- overall_comparison.png: 总体性能对比")
    print("- sequence_comparison.png: 各序列详细对比")
    print("- improvement_analysis.png: 改进效果分析")
